# 串口调试工具使用说明

## 功能特性

这是一个基于Slint UI框架开发的串口调试工具，具有以下功能：

### 主要功能
- **串口连接管理**：支持连接和断开串口
- **数据收发**：支持ASCII和HEX格式的数据发送和接收
- **实时显示**：实时显示收发数据，带时间戳
- **参数配置**：支持配置波特率、数据位、校验位、停止位
- **数据处理**：支持HEX/ASCII格式切换、自动滚动、数据清空

### 界面布局
1. **串口配置区域**：选择端口、波特率等参数
2. **数据接收区域**：显示接收到的数据
3. **数据发送区域**：输入要发送的数据
4. **状态栏**：显示连接状态和数据统计

## 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 配置串口参数
- **端口**：从下拉列表中选择可用的串口（如COM1、COM2等）
- **波特率**：选择合适的波特率（9600、19200、38400、57600、115200）
- **数据位**：选择数据位数（5、6、7、8）
- **校验位**：选择校验方式（None、Even、Odd）
- **停止位**：选择停止位数（1、1.5、2）

### 3. 连接串口
1. 点击"刷新端口"按钮获取最新的可用端口列表
2. 选择正确的串口参数
3. 点击"连接"按钮建立串口连接
4. 连接成功后按钮会变为"断开连接"

### 4. 数据接收
- 接收到的数据会自动显示在接收区域
- 每条数据都带有时间戳标记
- 可以选择"HEX显示"来以十六进制格式显示数据
- 可以选择"自动滚动"来自动滚动到最新数据
- 点击"清空接收"可以清空接收区域的所有数据

### 5. 数据发送
- 在发送框中输入要发送的数据
- 支持ASCII文本直接输入
- 支持HEX格式输入（如：0x41 0x42 或 4142）
- 点击"发送"按钮发送数据
- 发送的数据也会在接收区域显示，标记为TX

### 6. 数据格式
- **ASCII格式**：直接输入文本，如"Hello World"
- **HEX格式**：输入十六进制数据，如"0x48656C6C6F"或"48 65 6C 6C 6F"

## 注意事项

1. **端口占用**：确保选择的串口没有被其他程序占用
2. **参数匹配**：串口参数必须与对方设备匹配
3. **数据格式**：发送HEX数据时确保格式正确
4. **内存管理**：接收区域会自动限制显示行数以避免内存占用过大
5. **线程安全**：程序使用多线程处理数据收发，确保界面响应流畅

## 故障排除

### 连接失败
- 检查串口是否被其他程序占用
- 确认串口参数设置正确
- 尝试刷新端口列表

### 数据乱码
- 检查波特率设置是否正确
- 确认数据位、校验位、停止位设置
- 尝试切换HEX/ASCII显示格式

### 程序无响应
- 检查串口连接是否正常
- 尝试断开重连
- 重启程序

## 技术实现

- **UI框架**：Slint
- **串口通信**：pyserial
- **多线程**：Python threading
- **数据处理**：支持ASCII/HEX格式转换

## 依赖项

```toml
dependencies = [
    "slint>=1.11.0a1",
    "pyserial>=3.5",
]
```

## 开发环境

- Python >= 3.12
- Slint Python绑定
- pyserial库

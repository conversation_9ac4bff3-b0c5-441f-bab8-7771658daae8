import { Button, VerticalBox } from "std-widgets.slint";

export component AppWindow inherits Window {
    in-out property<int> counter: 0;
    callback request-increase-value();
    VerticalBox { 
        Text {
            text: "Counter: \{root.counter}";
        }
        Button {
            text: "Increase value";
            clicked => { 
                root.request-increase-value();
            }
        }
     }
}

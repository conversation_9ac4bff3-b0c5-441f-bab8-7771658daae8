import { Button, VerticalBox, HorizontalBox, ComboBox, LineEdit, TextEdit, CheckBox, GroupBox } from "std-widgets.slint";

export component AppWindow inherits Window {
    title: "串口调试工具";
    width: 1200px;
    height: 700px;

    // 串口配置属性
    in-out property<string> selected-port: "";
    in-out property<string> selected-baudrate: "9600";
    in-out property<string> selected-databits: "8";
    in-out property<string> selected-parity: "None";
    in-out property<string> selected-stopbits: "1";

    // 连接状态
    in-out property<bool> is-connected: false;

    // 数据显示
    in-out property<string> received-data: "";
    in-out property<string> send-data: "";

    // 显示格式
    in-out property<bool> hex-display: false;
    in-out property<bool> auto-scroll: true;

    // 回调函数
    callback connect-serial();
    callback disconnect-serial();
    callback send-serial-data();
    callback clear-received();
    callback refresh-ports();

    VerticalBox {
        padding: 10px;
        spacing: 10px;

        // 串口配置区域
        GroupBox {
            title: "串口配置";

            HorizontalBox {
                spacing: 10px;

                VerticalBox {
                    Text { text: "端口:"; }
                    ComboBox {
                        model: ["COM1", "COM2", "COM3", "COM4", "COM5"];
                        current-value <=> root.selected-port;
                    }
                }

                VerticalBox {
                    Text { text: "波特率:"; }
                    ComboBox {
                        model: ["9600", "19200", "38400", "57600", "115200"];
                        current-value <=> root.selected-baudrate;
                    }
                }

                VerticalBox {
                    Text { text: "数据位:"; }
                    ComboBox {
                        model: ["5", "6", "7", "8"];
                        current-value <=> root.selected-databits;
                    }
                }

                VerticalBox {
                    Text { text: "校验位:"; }
                    ComboBox {
                        model: ["None", "Even", "Odd"];
                        current-value <=> root.selected-parity;
                    }
                }

                VerticalBox {
                    Text { text: "停止位:"; }
                    ComboBox {
                        model: ["1", "1.5", "2"];
                        current-value <=> root.selected-stopbits;
                    }
                }

                VerticalBox {
                    spacing: 5px;
                    Button {
                        text: "刷新端口";
                        clicked => { root.refresh-ports(); }
                    }
                    Button {
                        text: root.is-connected ? "断开连接" : "连接";
                        enabled: root.selected-port != "";
                        clicked => {
                            if (root.is-connected) {
                                root.disconnect-serial();
                            } else {
                                root.connect-serial();
                            }
                        }
                    }
                }
            }
        }

        // 数据接收区域
        GroupBox {
            title: "数据接收";

            VerticalBox {
                spacing: 5px;

                HorizontalBox {
                    spacing: 10px;
                    CheckBox {
                        text: "HEX显示";
                        checked <=> root.hex-display;
                    }
                    CheckBox {
                        text: "自动滚动";
                        checked <=> root.auto-scroll;
                    }
                    Button {
                        text: "清空接收";
                        clicked => { root.clear-received(); }
                    }
                }

                TextEdit {
                    height: 200px;
                    text <=> root.received-data;
                    read-only: true;
                    wrap: word-wrap;
                }
            }
        }

        // 数据发送区域
        GroupBox {
            title: "数据发送";

            VerticalBox {
                spacing: 5px;

                HorizontalBox {
                    spacing: 10px;
                    LineEdit {
                        text <=> root.send-data;
                        placeholder-text: "输入要发送的数据...";
                    }
                    Button {
                        text: "发送";
                        enabled: root.is-connected && root.send-data != "";
                        clicked => { root.send-serial-data(); }
                    }
                }
            }
        }

        // 状态栏
        HorizontalBox {
            spacing: 20px;
            Text {
                text: root.is-connected ? "已连接 - " + root.selected-port : "未连接";
                color: root.is-connected ? green : red;
            }
            Text {
                text: "发送: 0 字节";
                color: blue;
            }
            Text {
                text: "接收: 0 字节";
                color: blue;
            }
        }
    }
}

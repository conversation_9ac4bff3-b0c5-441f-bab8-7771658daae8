import slint
import sys
import os
import serial
import serial.tools.list_ports
import threading
import time
from typing import Optional
from datetime import datetime


class SerialTool(slint.loader.ui.app_window.AppWindow):
    def __init__(self):
        super().__init__()
        self.serial_port: Optional[serial.Serial] = None
        self.read_thread: Optional[threading.Thread] = None
        self.is_reading = False

        # 数据统计
        self.bytes_sent = 0
        self.bytes_received = 0

        # 初始化串口端口列表
        self.refresh_serial_ports()

        # 绑定回调函数需要在super().__init__()之后
        # 这些回调函数会在类定义中通过装饰器自动绑定

    def refresh_serial_ports(self):
        """刷新可用串口列表"""
        ports = serial.tools.list_ports.comports()
        port_names = [port.device for port in ports]
        if not port_names:
            port_names = ["无可用端口"]

        # 这里需要更新UI中的ComboBox选项，但Slint目前不支持动态更新model
        # 作为替代方案，我们在连接时验证端口是否存在
        print(f"可用串口: {port_names}")
        return port_names

    @slint.callback
    def refresh_ports(self):
        """刷新端口回调"""
        self.refresh_serial_ports()

    @slint.callback
    def connect_serial(self):
        """连接串口"""
        try:
            port = self.selected_port
            baudrate = int(self.selected_baudrate)
            databits = int(self.selected_databits)

            # 解析校验位
            parity_map = {
                "None": serial.PARITY_NONE,
                "Even": serial.PARITY_EVEN,
                "Odd": serial.PARITY_ODD
            }
            parity = parity_map.get(self.selected_parity, serial.PARITY_NONE)

            # 解析停止位
            stopbits_map = {
                "1": serial.STOPBITS_ONE,
                "1.5": serial.STOPBITS_ONE_POINT_FIVE,
                "2": serial.STOPBITS_TWO
            }
            stopbits = stopbits_map.get(self.selected_stopbits, serial.STOPBITS_ONE)

            # 创建串口连接
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=databits,
                parity=parity,
                stopbits=stopbits,
                timeout=0.1
            )

            if self.serial_port.is_open:
                self.is_connected = True
                self.is_reading = True

                # 启动读取线程
                self.read_thread = threading.Thread(target=self._read_serial_data, daemon=True)
                self.read_thread.start()

                print(f"成功连接到 {port}")

        except Exception as e:
            print(f"连接失败: {str(e)}")
            self.is_connected = False

    @slint.callback
    def disconnect_serial(self):
        """断开串口连接"""
        try:
            self.is_reading = False

            if self.read_thread and self.read_thread.is_alive():
                self.read_thread.join(timeout=1.0)

            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()

            self.is_connected = False
            self.serial_port = None
            print("串口已断开")

        except Exception as e:
            print(f"断开连接时出错: {str(e)}")

    def _read_serial_data(self):
        """读取串口数据的线程函数"""
        while self.is_reading and self.serial_port and self.serial_port.is_open:
            try:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    self.bytes_received += len(data)

                    # 添加时间戳
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]

                    if self.hex_display:
                        # HEX格式显示
                        hex_data = ' '.join([f'{b:02X}' for b in data])
                        display_data = f"[{timestamp}] RX: {hex_data}\n"
                    else:
                        # ASCII格式显示
                        try:
                            ascii_data = data.decode('utf-8', errors='replace')
                            display_data = f"[{timestamp}] RX: {ascii_data}\n"
                        except:
                            display_data = f"[{timestamp}] RX: {str(data)}\n"

                    # 更新接收数据显示
                    current_data = self.received_data
                    new_data = current_data + display_data

                    # 如果启用自动滚动，限制显示的行数（避免内存占用过大）
                    if self.auto_scroll:
                        lines = new_data.split('\n')
                        if len(lines) > 1000:  # 保留最近1000行
                            new_data = '\n'.join(lines[-1000:])

                    self.received_data = new_data

                time.sleep(0.01)  # 避免CPU占用过高

            except Exception as e:
                print(f"读取数据时出错: {str(e)}")
                break

    @slint.callback
    def send_serial_data(self):
        """发送串口数据"""
        try:
            if not self.serial_port or not self.serial_port.is_open:
                print("串口未连接")
                return

            data_to_send = self.send_data
            if not data_to_send:
                return

            bytes_data = None
            display_format = ""

            # 检查是否为HEX格式数据
            if data_to_send.startswith("0x") or all(c in '0123456789ABCDEFabcdef ' for c in data_to_send.replace(" ", "")):
                try:
                    # 尝试作为HEX数据发送
                    hex_data = data_to_send.replace("0x", "").replace(" ", "")
                    if len(hex_data) % 2 == 0:
                        bytes_data = bytes.fromhex(hex_data)
                        display_format = "HEX"
                    else:
                        # 如果不是有效的HEX，作为ASCII发送
                        bytes_data = data_to_send.encode('utf-8')
                        display_format = "ASCII"
                except ValueError:
                    # HEX解析失败，作为ASCII发送
                    bytes_data = data_to_send.encode('utf-8')
                    display_format = "ASCII"
            else:
                # 作为ASCII数据发送
                bytes_data = data_to_send.encode('utf-8')
                display_format = "ASCII"

            # 发送数据
            self.serial_port.write(bytes_data)
            self.bytes_sent += len(bytes_data)

            # 在接收区域显示发送的数据
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            if self.hex_display:
                hex_display = ' '.join([f'{b:02X}' for b in bytes_data])
                tx_display = f"[{timestamp}] TX: {hex_display}\n"
            else:
                tx_display = f"[{timestamp}] TX: {data_to_send}\n"

            current_data = self.received_data
            self.received_data = current_data + tx_display

            # 清空发送框
            self.send_data = ""
            print(f"已发送 ({display_format}): {data_to_send}")

        except Exception as e:
            print(f"发送数据时出错: {str(e)}")

    @slint.callback
    def clear_received(self):
        """清空接收数据"""
        self.received_data = ""
        self.bytes_sent = 0
        self.bytes_received = 0
        print("接收数据已清空，统计数据已重置")


def main():
    app = SerialTool()
    app.show()

    try:
        app.run()
    except KeyboardInterrupt:
        print("程序被用户中断")
    finally:
        # 确保在程序退出时关闭串口
        if hasattr(app, 'serial_port') and app.serial_port:
            app.disconnect_serial()


if __name__ == "__main__":
    main()

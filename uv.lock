version = 1
revision = 1
requires-python = ">=3.12"

[[package]]
name = "slint"
version = "1.12.0a4"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/48/b7/46d813bdca39167a20a9ae5eb25962cb8190628c10a4c5e016b399933309/slint-1.12.0a4.tar.gz", hash = "sha256:f6eb02206cd1c67f060150155f3832fadbcc37a3445d86cc5b925c7dc0280493", size = 1906713 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a0/ee/dcb855826e3519640419b33bcd2aab3a1a57973d4978940d5b26f369b78b/slint-1.12.0a4-cp310-abi3-ios_13_0_arm64_iphoneos.whl", hash = "sha256:dbb370fcbefb62c82577552e84c10986d0fc94a17ad4620d650a47474af27e73", size = 8784248 },
    { url = "https://files.pythonhosted.org/packages/ad/50/482318fdb97028e5117d66b75ce106b156762754e61a1cca590fef751357/slint-1.12.0a4-cp310-abi3-ios_13_0_arm64_iphonesimulator.whl", hash = "sha256:7727bc03459f778f5fde65951f3a4fc51a73ee1a1d4822429ff71421cb8ce602", size = 8959458 },
    { url = "https://files.pythonhosted.org/packages/fd/af/4a8cb530e29573a3a188e45f2f05503d37117acc8dddf7f0dba6d6051d88/slint-1.12.0a4-cp310-abi3-macosx_11_0_arm64.whl", hash = "sha256:38da2a33807265ab9f3b1034c5c74cc3655ff02daec7ab991de9729749513191", size = 11733601 },
    { url = "https://files.pythonhosted.org/packages/57/b0/ed36fcd73708e65876376c73c04a0574f03095ec72dca4c857398756986e/slint-1.12.0a4-cp310-abi3-macosx_11_0_x86_64.whl", hash = "sha256:e60b985c75be6853da82a8696798a7c0fa775fb87cac69c3d07c2d689b99e028", size = 12291209 },
    { url = "https://files.pythonhosted.org/packages/84/53/3f7230f5d939b6415601930661dcb0d67269bb9570300b9fd3a9654cc9a0/slint-1.12.0a4-cp310-abi3-manylinux_2_31_aarch64.whl", hash = "sha256:53b61a5bcd330c1dbca17b91bc5b2bdf215d39274a8c2345ccf551f4056ef326", size = 16111463 },
    { url = "https://files.pythonhosted.org/packages/9d/0f/6f0edef1e9bfe0b71c1e14f12f50c3ad139a8599eb3af18313b1a5e13d1f/slint-1.12.0a4-cp310-abi3-manylinux_2_31_armv7l.whl", hash = "sha256:fdeb02692d78581ba33864bc592fe3e1e71e11e885c480c6d2c3808476ed1659", size = 15860281 },
    { url = "https://files.pythonhosted.org/packages/2e/03/cd766d135f037a7a43776cde2813b8a4679200154864cfff0e69bf8d7774/slint-1.12.0a4-cp310-abi3-manylinux_2_35_x86_64.whl", hash = "sha256:d4b815feb9169e9acc189e2d38ec27f2afa1ebae9808e0f8c2648380235bb086", size = 16742052 },
    { url = "https://files.pythonhosted.org/packages/e7/2e/8b63c2e3e4649e38eb72531bdf77ce6c98e14210a6f584f12cb2306d5c03/slint-1.12.0a4-cp310-abi3-win_amd64.whl", hash = "sha256:c9e1b3f86a458075508ac2ef569583cdb1871e949e266929bf31bae7b8ad1c99", size = 12229460 },
]

[[package]]
name = "slint-python-template"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "slint" },
]

[package.metadata]
requires-dist = [{ name = "slint", specifier = ">=1.11.0a1" }]
